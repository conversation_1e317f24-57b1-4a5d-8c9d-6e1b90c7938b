<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Comparison Hover Dots</title>
    <link rel="stylesheet" href="components/charts/snap-charts.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .chart-container {
            width: 100%;
            height: 400px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .instructions {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .instructions h3 {
            margin-top: 0;
            color: #1976d2;
        }
        .instructions ul {
            margin-bottom: 0;
        }
    </style>
</head>
<body>
    <div class="instructions">
        <h3>Testing Comparison Hover Dots</h3>
        <ul>
            <li>Hover over the main columns (left) - should show green dots</li>
            <li>Hover over the comparison columns (right) - should show gray dots (#606F95)</li>
            <li>Hover over the gap between columns - should show both dots</li>
            <li>Check that dots have the correct colors and appear/disappear properly</li>
        </ul>
    </div>

    <div class="test-container">
        <h2>Stacked Column Chart with Compare Mode</h2>
        <div class="chart-container" id="test-chart"></div>
    </div>

    <script src="components/charts/snap-charts.js"></script>
    <script>
        // Test data for main columns
        const testData = [
            {
                sales: 45,
                royalties: 12.5,
                values: [25, 15, 5],
                labels: ['US', 'UK', 'DE'],
                month: 'Jan',
                year: '2024'
            },
            {
                sales: 62,
                royalties: 18.3,
                values: [35, 20, 7],
                labels: ['US', 'UK', 'DE'],
                month: 'Feb',
                year: '2024'
            },
            {
                sales: 38,
                royalties: 9.8,
                values: [20, 12, 6],
                labels: ['US', 'UK', 'DE'],
                month: 'Mar',
                year: '2024'
            },
            {
                sales: 71,
                royalties: 22.1,
                values: [40, 25, 6],
                labels: ['US', 'UK', 'DE'],
                month: 'Apr',
                year: '2024'
            }
        ];

        // Test data for comparison columns
        const compareData = [
            {
                sales: 38,
                royalties: 10.2,
                values: [20, 12, 6],
                labels: ['US', 'UK', 'DE'],
                month: 'Jan',
                year: '2023'
            },
            {
                sales: 55,
                royalties: 15.8,
                values: [30, 18, 7],
                labels: ['US', 'UK', 'DE'],
                month: 'Feb',
                year: '2023'
            },
            {
                sales: 42,
                royalties: 11.5,
                values: [22, 15, 5],
                labels: ['US', 'UK', 'DE'],
                month: 'Mar',
                year: '2023'
            },
            {
                sales: 59,
                royalties: 17.9,
                values: [32, 20, 7],
                labels: ['US', 'UK', 'DE'],
                month: 'Apr',
                year: '2023'
            }
        ];

        // Create chart with comparison mode
        const chartContainer = document.getElementById('test-chart');
        const chart = new SnapChart(chartContainer, testData, {
            type: 'stacked-column',
            compareMode: true,
            compareData: compareData,
            title: 'Test Chart - Hover over columns to see dots',
            subtitle: 'Main columns (left) should show green dots, comparison columns (right) should show gray dots'
        });

        chart.render();

        console.log('Test chart created with comparison mode enabled');
        console.log('Main data points:', testData.length);
        console.log('Comparison data points:', compareData.length);
    </script>
</body>
</html>
